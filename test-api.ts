// Simple test script to verify API endpoints
const BASE_URL = 'http://localhost:3000';
const ADMIN_TOKEN = 'admin-token-123'; // From .env file

interface TestResult {
  endpoint: string;
  method: string;
  status: number;
  success: boolean;
  data?: any;
  error?: string;
}

async function testEndpoint(
  endpoint: string,
  method: string = 'GET',
  body?: any,
  headers?: Record<string, string>
): Promise<TestResult> {
  try {
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();

    return {
      endpoint,
      method,
      status: response.status,
      success: data.success || false,
      data: data.data,
      error: data.error,
    };
  } catch (error) {
    return {
      endpoint,
      method,
      status: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function runTests() {
  console.log('🧪 Testing Inspirator API...\n');

  const results: TestResult[] = [];

  // Test 1: Health check
  console.log('1. Testing health check...');
  const healthResult = await testEndpoint('/health');
  results.push(healthResult);
  console.log(`   Status: ${healthResult.status}, Success: ${healthResult.success}\n`);

  // Test 2: Upload a sentence
  console.log('2. Testing sentence upload...');
  const uploadData = {
    content: 'This is a test sentence for the API.',
    name: 'Test User',
    img_url: 'https://example.com/test-image.jpg',
  };
  const uploadResult = await testEndpoint('/api/sentences/upload', 'POST', uploadData);
  results.push(uploadResult);
  console.log(`   Status: ${uploadResult.status}, Success: ${uploadResult.success}`);
  
  let sentenceId: string | null = null;
  if (uploadResult.success && uploadResult.data) {
    sentenceId = uploadResult.data.id;
    console.log(`   Created sentence ID: ${sentenceId}\n`);
  } else {
    console.log(`   Error: ${uploadResult.error}\n`);
  }

  // Test 3: Get sentence by ID
  if (sentenceId) {
    console.log('3. Testing get sentence by ID...');
    const getByIdResult = await testEndpoint(`/api/sentences/${sentenceId}`);
    results.push(getByIdResult);
    console.log(`   Status: ${getByIdResult.status}, Success: ${getByIdResult.success}\n`);
  }

  // Test 4: Get random sentence
  console.log('4. Testing get random sentence...');
  const randomResult = await testEndpoint('/api/sentences/random');
  results.push(randomResult);
  console.log(`   Status: ${randomResult.status}, Success: ${randomResult.success}\n`);

  // Test 5: Get all sentences
  console.log('5. Testing get all sentences...');
  const allResult = await testEndpoint('/api/sentences/all');
  results.push(allResult);
  console.log(`   Status: ${allResult.status}, Success: ${allResult.success}`);
  if (allResult.success && allResult.data) {
    console.log(`   Found ${allResult.data.length} sentences\n`);
  }

  // Test 6: Get sentence range
  console.log('6. Testing get sentence range...');
  const rangeResult = await testEndpoint('/api/sentences/range?skip=0&limit=5');
  results.push(rangeResult);
  console.log(`   Status: ${rangeResult.status}, Success: ${rangeResult.success}`);
  if (rangeResult.success && rangeResult.data) {
    console.log(`   Found ${rangeResult.data.sentences.length} sentences in range\n`);
  }

  // Test 7: Delete sentence (requires admin token)
  if (sentenceId) {
    console.log('7. Testing delete sentence...');
    const deleteResult = await testEndpoint(
      `/api/sentences/${sentenceId}`,
      'DELETE',
      null,
      { Authorization: `Bearer ${ADMIN_TOKEN}` }
    );
    results.push(deleteResult);
    console.log(`   Status: ${deleteResult.status}, Success: ${deleteResult.success}\n`);
  }

  // Test 8: Test validation errors
  console.log('8. Testing validation errors...');
  const invalidUploadResult = await testEndpoint('/api/sentences/upload', 'POST', {
    content: '', // Invalid: empty content
    name: 'Test',
    img_url: 'not-a-url', // Invalid: not a valid URL
  });
  results.push(invalidUploadResult);
  console.log(`   Status: ${invalidUploadResult.status}, Success: ${invalidUploadResult.success}`);
  console.log(`   Expected validation errors: ${invalidUploadResult.error}\n`);

  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  const passed = results.filter(r => r.success || (r.status >= 200 && r.status < 300)).length;
  const total = results.length;
  console.log(`Passed: ${passed}/${total}`);
  
  results.forEach((result, index) => {
    const status = result.success || (result.status >= 200 && result.status < 300) ? '✅' : '❌';
    console.log(`${status} ${result.method} ${result.endpoint} (${result.status})`);
  });
}

// Run tests if this file is executed directly
if (import.meta.main) {
  runTests().catch(console.error);
}
