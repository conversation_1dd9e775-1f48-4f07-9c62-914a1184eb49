{"name": "inspirator-api", "module": "index.ts", "type": "module", "private": true, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/bun": "latest", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "uuid": "^11.1.0"}}