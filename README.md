# Inspirator API

A TypeScript API built with Bun and MongoDB for managing sentence uploads with user information and images.

## Features

- **Upload sentences** with content, name, and image URL
- **Retrieve sentences** by ID, random selection, or paginated lists
- **Delete sentences** with admin token authentication
- **IP tracking** for each upload
- **Input validation** and error handling
- **CORS support** for web applications

## Installation

1. Clone the repository
2. Install dependencies:
```bash
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Update the `.env` file with your MongoDB connection string and other settings.

5. Make sure MongoDB is running on your system.

## Usage

Start the server:
```bash
bun run index.ts
```

The API will be available at `http://localhost:3000` (or the port specified in your `.env` file).

## API Endpoints

### Health Check
- **GET** `/health` - Check server and database status

### Sentence Operations

#### Upload Sentence
- **POST** `/api/sentences/upload`
- **Body:**
```json
{
  "content": "Your sentence content here",
  "name": "Author name",
  "img_url": "https://example.com/image.jpg"
}
```

#### Get Sentence by ID
- **GET** `/api/sentences/:id`
- Returns a specific sentence by its UUID

#### Get Random Sentence
- **GET** `/api/sentences/random`
- Returns a randomly selected sentence

#### Get All Sentences
- **GET** `/api/sentences/all`
- Returns all sentences, sorted by timestamp (newest first)

#### Get Sentence Range (Paginated)
- **GET** `/api/sentences/range?skip=0&limit=10`
- **Query Parameters:**
  - `skip`: Number of sentences to skip (default: 0)
  - `limit`: Maximum number of sentences to return (1-100, default: 10)

#### Delete Sentence (Admin Only)
- **DELETE** `/api/sentences/:id`
- **Headers:** `Authorization: Bearer <admin_token>`
- Requires admin token from environment variables

## Data Structure

Each sentence contains:
- `id`: Unique UUID identifier
- `content`: The sentence text (max 1000 characters)
- `name`: Author name (max 100 characters)
- `img_url`: Image URL (max 500 characters, must be valid URL)
- `timestamp`: ISO timestamp of creation
- `ip`: IP address of the uploader

## Testing

Run the test suite:
```bash
bun run test-api.ts
```

Make sure the server is running before executing tests.

## Environment Variables

- `MONGODB_URI`: MongoDB connection string
- `MONGODB_DB_NAME`: Database name
- `PORT`: Server port (default: 3000)
- `JWT_SECRET`: Secret for JWT token generation
- `ADMIN_TOKEN`: Token required for delete operations
- `CORS_ORIGIN`: Allowed CORS origin (default: *)

## Error Handling

The API returns consistent error responses:
```json
{
  "success": false,
  "error": "Error message",
  "data": {
    "errors": ["Detailed validation errors"]
  }
}
```

## Development

This project uses:
- **Bun** - JavaScript runtime and package manager
- **TypeScript** - Type safety
- **MongoDB** - Database
- **UUID v4** - Unique identifiers
