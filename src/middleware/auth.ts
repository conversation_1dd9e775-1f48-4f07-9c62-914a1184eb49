import jwt from 'jsonwebtoken';
import { ApiResponse } from '../types/sentence';

export interface AuthRequest extends Request {
  user?: any;
}

export function authenticateToken(req: AuthRequest): ApiResponse | null {
  const authHeader = req.headers.get('authorization');
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return {
      success: false,
      error: 'Access token required',
    };
  }

  try {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    const decoded = jwt.verify(token, jwtSecret);
    req.user = decoded;
    return null; // No error, authentication successful
  } catch (error) {
    return {
      success: false,
      error: 'Invalid or expired token',
    };
  }
}

export function authenticateAdminToken(req: Request): ApiResponse | null {
  const authHeader = req.headers.get('authorization');
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return {
      success: false,
      error: 'Admin token required',
    };
  }

  const adminToken = process.env.ADMIN_TOKEN;
  if (!adminToken) {
    return {
      success: false,
      error: 'Admin token not configured',
    };
  }

  if (token !== adminToken) {
    return {
      success: false,
      error: 'Invalid admin token',
    };
  }

  return null; // No error, authentication successful
}

export function generateToken(payload: any): string {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET not configured');
  }
  
  return jwt.sign(payload, jwtSecret, { expiresIn: '24h' });
}
