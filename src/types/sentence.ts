import { ObjectId } from 'mongodb';

export interface Sentence {
  _id?: ObjectId;
  id: string;
  content: string;
  name: string;
  img_url: string;
  timestamp: Date;
  ip: string;
}

export interface CreateSentenceRequest {
  content: string;
  name: string;
  img_url: string;
}

export interface SentenceResponse {
  id: string;
  content: string;
  name: string;
  img_url: string;
  timestamp: string;
  ip: string;
}

export interface GetRangeQuery {
  skip?: number;
  limit?: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
