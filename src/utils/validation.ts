export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function validateSentenceContent(content: string): ValidationResult {
  const errors: string[] = [];

  if (!content || typeof content !== 'string') {
    errors.push('Content is required and must be a string');
  } else {
    const trimmed = content.trim();
    if (trimmed.length === 0) {
      errors.push('Content cannot be empty');
    } else if (trimmed.length > 1000) {
      errors.push('Content must be 1000 characters or less');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateSentenceName(name: string): ValidationResult {
  const errors: string[] = [];

  if (!name || typeof name !== 'string') {
    errors.push('Name is required and must be a string');
  } else {
    const trimmed = name.trim();
    if (trimmed.length === 0) {
      errors.push('Name cannot be empty');
    } else if (trimmed.length > 100) {
      errors.push('Name must be 100 characters or less');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateImageUrl(imgUrl: string): ValidationResult {
  const errors: string[] = [];

  if (!imgUrl || typeof imgUrl !== 'string') {
    errors.push('Image URL is required and must be a string');
  } else {
    const trimmed = imgUrl.trim();
    if (trimmed.length === 0) {
      errors.push('Image URL cannot be empty');
    } else if (trimmed.length > 500) {
      errors.push('Image URL must be 500 characters or less');
    } else {
      // Basic URL validation
      try {
        new URL(trimmed);
      } catch {
        errors.push('Image URL must be a valid URL');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validatePaginationParams(skip: number, limit: number): ValidationResult {
  const errors: string[] = [];

  if (!Number.isInteger(skip) || skip < 0) {
    errors.push('Skip must be a non-negative integer');
  }

  if (!Number.isInteger(limit) || limit < 1) {
    errors.push('Limit must be a positive integer');
  } else if (limit > 100) {
    errors.push('Limit cannot exceed 100');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateUUID(id: string): ValidationResult {
  const errors: string[] = [];

  if (!id || typeof id !== 'string') {
    errors.push('ID is required and must be a string');
  } else {
    // Basic UUID v4 validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      errors.push('ID must be a valid UUID');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
