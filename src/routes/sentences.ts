import { sentenceService } from '../services/sentenceService';
import { authenticateAdminToken } from '../middleware/auth';
import { CreateSentenceRequest, ApiResponse, GetRangeQuery } from '../types/sentence';
import {
  validateSentenceContent,
  validateSentenceName,
  validateImageUrl,
  validatePaginationParams,
  validateUUID,
} from '../utils/validation';

function getClientIP(req: Request): string {
  // Try to get IP from various headers
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return 'unknown';
}

function validateSentenceData(data: any): { isValid: boolean; data?: CreateSentenceRequest; errors?: string[] } {
  if (!data || typeof data !== 'object') {
    return { isValid: false, errors: ['Invalid request body'] };
  }

  const { content, name, img_url } = data;
  const errors: string[] = [];

  const contentValidation = validateSentenceContent(content);
  if (!contentValidation.isValid) {
    errors.push(...contentValidation.errors);
  }

  const nameValidation = validateSentenceName(name);
  if (!nameValidation.isValid) {
    errors.push(...nameValidation.errors);
  }

  const imgUrlValidation = validateImageUrl(img_url);
  if (!imgUrlValidation.isValid) {
    errors.push(...imgUrlValidation.errors);
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    data: {
      content: content.trim(),
      name: name.trim(),
      img_url: img_url.trim(),
    },
  };
}

export async function handleUploadSentence(req: Request): Promise<Response> {
  try {
    const body = await req.json();
    const validation = validateSentenceData(body);

    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        data: { errors: validation.errors },
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const ip = getClientIP(req);
    const sentence = await sentenceService.createSentence(validation.data!, ip);

    const response: ApiResponse = {
      success: true,
      data: sentence,
      message: 'Sentence uploaded successfully',
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error uploading sentence:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function handleGetSentenceById(req: Request, id: string): Promise<Response> {
  try {
    const idValidation = validateUUID(id);
    if (!idValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid ID format',
        data: { errors: idValidation.errors },
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const sentence = await sentenceService.getSentenceById(id);

    if (!sentence) {
      const response: ApiResponse = {
        success: false,
        error: 'Sentence not found',
      };
      return new Response(JSON.stringify(response), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const response: ApiResponse = {
      success: true,
      data: sentence,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error getting sentence by ID:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function handleGetRandomSentence(): Promise<Response> {
  try {
    const sentence = await sentenceService.getRandomSentence();

    if (!sentence) {
      const response: ApiResponse = {
        success: false,
        error: 'No sentences found',
      };
      return new Response(JSON.stringify(response), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const response: ApiResponse = {
      success: true,
      data: sentence,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error getting random sentence:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function handleGetAllSentences(): Promise<Response> {
  try {
    const sentences = await sentenceService.getAllSentences();

    const response: ApiResponse = {
      success: true,
      data: sentences,
      message: `Retrieved ${sentences.length} sentences`,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error getting all sentences:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function handleGetSentenceRange(req: Request): Promise<Response> {
  try {
    const url = new URL(req.url);
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    const paginationValidation = validatePaginationParams(skip, limit);
    if (!paginationValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid pagination parameters',
        data: { errors: paginationValidation.errors },
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const query: GetRangeQuery = { skip, limit };
    const sentences = await sentenceService.getSentenceRange(query);
    const totalCount = await sentenceService.getSentenceCount();

    const response: ApiResponse = {
      success: true,
      data: {
        sentences,
        pagination: {
          skip,
          limit,
          total: totalCount,
          hasMore: skip + limit < totalCount,
        },
      },
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error getting sentence range:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function handleDeleteSentence(req: Request, id: string): Promise<Response> {
  try {
    // Check admin authentication
    const authError = authenticateAdminToken(req);
    if (authError) {
      return new Response(JSON.stringify(authError), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate ID format
    const idValidation = validateUUID(id);
    if (!idValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid ID format',
        data: { errors: idValidation.errors },
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const deleted = await sentenceService.deleteSentence(id);

    if (!deleted) {
      const response: ApiResponse = {
        success: false,
        error: 'Sentence not found',
      };
      return new Response(JSON.stringify(response), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Sentence deleted successfully',
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error deleting sentence:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
