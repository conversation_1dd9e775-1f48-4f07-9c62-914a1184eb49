import { ObjectId } from 'mongodb';
import { database } from '../config/database';
import { Sentence, CreateSentenceRequest, SentenceResponse, GetRangeQuery } from '../types/sentence';
import { v4 as uuidv4 } from 'uuid';

export class SentenceService {
  private readonly collectionName = 'sentences';

  private getCollection() {
    return database.getDb().collection<Sentence>(this.collectionName);
  }

  private formatSentenceResponse(sentence: Sentence): SentenceResponse {
    return {
      id: sentence.id,
      content: sentence.content,
      name: sentence.name,
      img_url: sentence.img_url,
      timestamp: sentence.timestamp.toISOString(),
      ip: sentence.ip,
    };
  }

  async createSentence(data: CreateSentenceRequest, ip: string): Promise<SentenceResponse> {
    const sentence: Sentence = {
      id: uuidv4(),
      content: data.content,
      name: data.name,
      img_url: data.img_url,
      timestamp: new Date(),
      ip: ip,
    };

    const collection = this.getCollection();
    const result = await collection.insertOne(sentence);
    
    if (!result.insertedId) {
      throw new Error('Failed to create sentence');
    }

    return this.formatSentenceResponse(sentence);
  }

  async getSentenceById(id: string): Promise<SentenceResponse | null> {
    const collection = this.getCollection();
    const sentence = await collection.findOne({ id });
    
    if (!sentence) {
      return null;
    }

    return this.formatSentenceResponse(sentence);
  }

  async getRandomSentence(): Promise<SentenceResponse | null> {
    const collection = this.getCollection();
    const pipeline = [{ $sample: { size: 1 } }];
    const result = await collection.aggregate<Sentence>(pipeline).toArray();
    
    if (result.length === 0) {
      return null;
    }

    return this.formatSentenceResponse(result[0]);
  }

  async getAllSentences(): Promise<SentenceResponse[]> {
    const collection = this.getCollection();
    const sentences = await collection.find({}).sort({ timestamp: -1 }).toArray();
    
    return sentences.map(sentence => this.formatSentenceResponse(sentence));
  }

  async getSentenceRange(query: GetRangeQuery): Promise<SentenceResponse[]> {
    const collection = this.getCollection();
    const skip = query.skip || 0;
    const limit = query.limit || 10;
    
    const sentences = await collection
      .find({})
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();
    
    return sentences.map(sentence => this.formatSentenceResponse(sentence));
  }

  async deleteSentence(id: string): Promise<boolean> {
    const collection = this.getCollection();
    const result = await collection.deleteOne({ id });
    
    return result.deletedCount === 1;
  }

  async getSentenceCount(): Promise<number> {
    const collection = this.getCollection();
    return await collection.countDocuments();
  }
}

export const sentenceService = new SentenceService();
