import { database } from './src/config/database';
import {
  handleUploadSentence,
  handleGetSentenceById,
  handleGetRandomSentence,
  handleGetAllSentences,
  handleGetSentenceRange,
  handleDeleteSentence,
} from './src/routes/sentences';
import { ApiResponse } from './src/types/sentence';

// Load environment variables
const PORT = process.env.PORT || 3000;
const CORS_ORIGIN = process.env.CORS_ORIGIN || '*';

// CORS headers
function setCorsHeaders(response: Response): Response {
  response.headers.set('Access-Control-Allow-Origin', CORS_ORIGIN);
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  return response;
}

// Handle CORS preflight requests
function handleCors(req: Request): Response | null {
  if (req.method === 'OPTIONS') {
    const response = new Response(null, { status: 204 });
    return setCorsHeaders(response);
  }
  return null;
}

// Route handler
async function handleRequest(req: Request): Promise<Response> {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  try {
    // Health check endpoint
    if (path === '/health' && method === 'GET') {
      const response: ApiResponse = {
        success: true,
        message: 'Server is healthy',
        data: {
          timestamp: new Date().toISOString(),
          database: database.isConnected() ? 'connected' : 'disconnected',
        },
      };
      return setCorsHeaders(new Response(JSON.stringify(response), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }));
    }

    // API routes
    if (path === '/api/sentences/upload' && method === 'POST') {
      const response = await handleUploadSentence(req);
      return setCorsHeaders(response);
    }

    if (path === '/api/sentences/random' && method === 'GET') {
      const response = await handleGetRandomSentence();
      return setCorsHeaders(response);
    }

    if (path === '/api/sentences/all' && method === 'GET') {
      const response = await handleGetAllSentences();
      return setCorsHeaders(response);
    }

    if (path === '/api/sentences/range' && method === 'GET') {
      const response = await handleGetSentenceRange(req);
      return setCorsHeaders(response);
    }

    // Dynamic routes
    const sentenceIdMatch = path.match(/^\/api\/sentences\/([^\/]+)$/);
    if (sentenceIdMatch) {
      const id = sentenceIdMatch[1];

      if (method === 'GET') {
        const response = await handleGetSentenceById(req, id);
        return setCorsHeaders(response);
      }

      if (method === 'DELETE') {
        const response = await handleDeleteSentence(req, id);
        return setCorsHeaders(response);
      }
    }

    // 404 for unmatched routes
    const response: ApiResponse = {
      success: false,
      error: 'Route not found',
    };
    return setCorsHeaders(new Response(JSON.stringify(response), {
      status: 404,
      headers: { 'Content-Type': 'application/json' },
    }));

  } catch (error) {
    console.error('Unhandled error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
    };
    return setCorsHeaders(new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    }));
  }
}

// Initialize server
async function startServer() {
  try {
    // Connect to database
    await database.connect();

    // Start server
    const server = Bun.serve({
      port: PORT,
      fetch: handleRequest,
    });

    console.log(`🚀 Server running on http://localhost:${server.port}`);
    console.log('📚 API Documentation:');
    console.log('  POST   /api/sentences/upload     - Upload a new sentence');
    console.log('  GET    /api/sentences/:id        - Get sentence by ID');
    console.log('  GET    /api/sentences/random     - Get random sentence');
    console.log('  GET    /api/sentences/all        - Get all sentences');
    console.log('  GET    /api/sentences/range      - Get sentences with pagination');
    console.log('  DELETE /api/sentences/:id        - Delete sentence (requires admin token)');
    console.log('  GET    /health                   - Health check');

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await database.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Shutting down server...');
  await database.disconnect();
  process.exit(0);
});

// Start the server
startServer();